﻿using JNPF.Common.Core.Manager;
using JNPF.Common.Core.Security;
using JNPF.ClayObject;
using JNPF.Common.Configuration;
using JNPF.Common.Models.NPOI;
using JNPF.DataEncryption;
using JNPF.Common.Enum;
using JNPF.Common.Extension;
using JNPF.Common.Filter;
using JNPF.Common.Security;
using JNPF.DependencyInjection;
using JNPF.DynamicApiController;
using JNPF.FriendlyException;
using JNPF.SubDev.Entitys.Dto.Mysamples;
using JNPF.SubDev.Entitys;
using JNPF.SubDev.Interfaces;
using JNPF.Systems.Entitys.Permission;
using Mapster;
using Microsoft.AspNetCore.Mvc;
using SqlSugar;
using Senparc.CO2NET.Extensions;
using System.Linq;
using JNPF.Systems.Interfaces.Permission;

namespace JNPF.SubDev;

/// <summary>
/// 业务实现：门店信息.
/// </summary>
[ApiDescriptionSettings(Tag = "SubDev", Name = "Mysamples", Order = 200)]
[Route("api/SubDev/[controller]")]
public class MysamplesService : IMysamplesService, IDynamicApiController, ITransient
{
    /// <summary>
    /// 服务基础仓储.
    /// </summary>
    private readonly ISqlSugarRepository<MysamplesEntity> _repository;
    private readonly IOrganizeService _organizeIService;

    private readonly ISqlSugarRepository<StoresEntity> _stores;
    private readonly ISqlSugarRepository<OrganizeEntity> _organize;

    /// <summary>
    /// 多租户事务.
    /// </summary>
    private readonly ITenant _db;

    /// <summary>
    /// 用户管理.
    /// </summary>
    private readonly IUserManager _userManager;

    /// <summary>
    /// 初始化一个<see cref="MysamplesService"/>类型的新实例.
    /// </summary>
    public MysamplesService(
        ISqlSugarRepository<MysamplesEntity> mysamplesRepository,
        ISqlSugarClient context,
        IUserManager userManager, ISqlSugarRepository<StoresEntity> stores,
        ISqlSugarRepository<OrganizeEntity> organize, IOrganizeService organizeIService)
    {
        _repository = mysamplesRepository;
        _db = context.AsTenant();
        _userManager = userManager;
        _stores = stores;
        _organize = organize;
        _organizeIService = organizeIService;
    }

    /// <summary>
    /// 获取门店信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <returns></returns>
    [HttpGet("{id}")]
    public async Task<dynamic> GetInfo(string id)
    {
        var output = (await _repository.FirstOrDefaultAsync(x => x.Id == id)).Adapt<MysamplesInfoOutput>();

        var mysamplesburnList = await _repository.Context.Queryable<MysamplesburnEntity>()
            .Where(w => w.MainId == output.id).ToListAsync();
        output.mysamplesburnList = mysamplesburnList.Adapt<List<MysamplesburnInfoOutput>>();

        return output;
    }

    /// <summary>
    /// 获取门店信息列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("")]
    public async Task<dynamic> GetList([FromQuery] MysamplesListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<MysamplesListOutput>(input.menuId, "id",
                _userManager.UserOrigin.Equals("pc") ? true : true);
        }

        var sidx = input.sidx == null ? "Id" : input.sidx;

        // 先构建基础查询
        var query = _repository.Context.Queryable<MysamplesEntity>()
            .LeftJoin<StoresEntity>((m, s) => m.Terminalid == s.Terminal);

        // 添加条件过滤
        if (!string.IsNullOrEmpty(input.region))
            query = query.Where((m, s) => m.Region.Equals(input.region));

        if (!string.IsNullOrEmpty(input.agency))
            query = query.Where((m, s) => m.Agency.Equals(input.agency));

        if (!string.IsNullOrEmpty(input.terminalid))
            query = query.Where((m, s) => m.Terminalid.Contains(input.terminalid));

        if (!string.IsNullOrEmpty(input.keyword))
            query = query.Where((m, s) => m.Region.Contains(input.keyword)
                || m.Agency.Contains(input.keyword)
                || m.Terminalid.Contains(input.keyword));

        // 过滤闭店
        query = query.Where((m, s) => s.Deleted != "闭店");

        // 应用数据权限过滤 - 使用单表查询避免字段歧义
        if (authorizeWhere.Count > 0)
        {
            var authorizedIds = await _repository.Context.Queryable<MysamplesEntity>()
                .Where(authorizeWhere)
                .Select(x => x.Id)
                .ToListAsync();
            query = query.Where((m, s) => authorizedIds.Contains(m.Id));
        }

        var data = await query
            .Select((m, s) => new MysamplesListOutput
            {
                id = m.Id,
                terminalid = m.Terminalid,
                organizationId = m.OrganizationId,
                positionId = m.PositionId,
                region = m.Region,
                agency = m.Agency,
                importantoxygen = SqlFunc.IIF(m.Importantoxygen == 0, "无", "有"),
                importantenjoy = SqlFunc.IIF(m.Importantenjoy == 0, "无", "有"),
                importantplentywater = SqlFunc.IIF(m.Importantplentywater == 0, "无", "有"),
                importantkitchen = SqlFunc.IIF(m.Importantkitchen == 0, "无", "有"),
                importantcommon = m.Importantcommon,
                importantcommonamount = m.Importantcommonamount,
                combinedSupply = SqlFunc.IIF(m.combinedSupply == 0, "无", "有"),
                bigwater = SqlFunc.IIF(m.bigwater == 0, "无", "有"),
            })
            .MergeTable()
            .OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.id)
            .OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort)
            .ToPagedListAsync(input.currentPage, input.pageSize);


        var storeDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.Terminal);
        var levelDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Terminal, x => x.Level1);
        var signedclientDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Terminal, x => x.Signedclient);
        var organizeDic = await _organize.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.FullName);

        foreach (var item in data.list)
        {
            if (storeDic.ContainsKey(item.terminalid))
            {
                item.terminalid = storeDic[item.terminalid].ToString();
                ;
            }

            if (organizeDic.ContainsKey(item.organizationId))
            {
                item.organizationId = organizeDic[item.organizationId].ToString();
                ;
            }

            if (levelDic.ContainsKey(item.terminalid))
            {
                if (levelDic[item.terminalid] is not null)
                    item.level1 = levelDic[item.terminalid].ToString();
                ;
            }


            if (signedclientDic.ContainsKey(item.terminalid))
            {
                if (signedclientDic[item.terminalid] is not null)
                    item.signedclient = signedclientDic[item.terminalid].ToString();
            }
        }

        if (!string.IsNullOrEmpty(input.terminalid))
        {
            data.list = data.list.Where(i => i.terminalid.Contains(input.terminalid)).ToList();
        }


        return PageResult<MysamplesListOutput>.SqlSugarPageResult(data);
    }

    /// <summary>
    /// 新建门店信息.
    /// </summary>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPost("")]
    public async Task Create([FromBody] MysamplesCrInput input)
    {
        var store = _repository.FirstOrDefaultAsync(i => i.Terminalid == input.terminalid);
        if (store.Result is not null) throw Oops.Oh(ErrorCode.COM1004);


        var entity = input.Adapt<MysamplesEntity>();
        entity.Id = SnowflakeIdHelper.NextId();
        // entity.OrganizationId = _userManager.User.OrganizeId;
        entity.PositionId = _userManager.User.PositionId;

        // 生成organizeId
        var organizeDic = await GetOrganizeDicTask();

        if (entity.Agency != null && organizeDic.ContainsKey(entity.Agency))
        {
            entity.OrganizationId = organizeDic[entity.Agency];
        }

        try
        {
            // 开启事务
            _db.BeginTran();

            var newEntity = await _repository.Context.Insertable<MysamplesEntity>(entity)
                .IgnoreColumns(ignoreNullColumn: true).ExecuteReturnEntityAsync();

            var mysamplesburnEntityList = input.mysamplesburnList.Adapt<List<MysamplesburnEntity>>();
            if (mysamplesburnEntityList != null)
            {
                foreach (var item in mysamplesburnEntityList)
                {
                    item.Id = SnowflakeIdHelper.NextId();
                    item.MainId = newEntity.Id;
                    item.Region = newEntity.Region;
                    item.Agency = newEntity.Agency;
                    item.Terminalid = newEntity.Terminalid;
                    item.OrganizationId = newEntity.OrganizationId;
                    item.CreatorUserId = _userManager.UserId;
                    item.CreatorTime = DateTime.Today;
                }

                await _repository.Context.Insertable<MysamplesburnEntity>(mysamplesburnEntityList)
                    .ExecuteCommandAsync();
            }

            // 关闭事务
            _db.CommitTran();
        }
        catch (Exception)
        {
            // 回滚事务
            _db.RollbackTran();

            throw Oops.Oh(ErrorCode.COM1000);
        }
    }
    /// <summary>
    /// 获取组织机构名称和id字典
    /// </summary>
    /// <returns></returns>
    private async Task<Dictionary<string, string>> GetOrganizeDicTask()
    {
        var data = await _organizeIService.GetListAsync();
        var dic = data.ToDictionary(i => i.FullName, i => i.Id);
        return dic;
    }
    /// <summary>
    /// 获取门店信息无分页列表.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    private async Task<dynamic> GetNoPagingList([FromQuery] MysamplesListQueryInput input)
    {
        var authorizeWhere = new List<IConditionalModel>();

        // 数据权限过滤
        if (_userManager.User.IsAdministrator == 0)
        {
            authorizeWhere = await _userManager.GetConditionAsync<MysamplesListOutput>(input.menuId, "id",
                _userManager.UserOrigin.Equals("pc") ? true : false);
        }


        // 先构建基础查询
        var query = _repository.Context.Queryable<MysamplesEntity>()
            .LeftJoin<StoresEntity>((m, s) => m.Terminalid == s.Terminal);

        // 添加条件过滤
        if (!string.IsNullOrEmpty(input.region))
            query = query.Where((m, s) => m.Region.Equals(input.region));

        if (!string.IsNullOrEmpty(input.agency))
            query = query.Where((m, s) => m.Agency.Equals(input.agency));

        if (!string.IsNullOrEmpty(input.terminalid))
            query = query.Where((m, s) => m.Terminalid.Contains(input.terminalid));

        if (!string.IsNullOrEmpty(input.keyword))
            query = query.Where((m, s) => m.Region.Contains(input.keyword)
                || m.Agency.Contains(input.keyword));

        // 过滤闭店
        query = query.Where((m, s) => s.Deleted != "闭店");

        // 应用数据权限过滤 - 使用单表查询避免字段歧义
        if (authorizeWhere.Count > 0)
        {
            var authorizedIds = await _repository.Context.Queryable<MysamplesEntity>()
                .Where(authorizeWhere)
                .Select(x => x.Id)
                .ToListAsync();
            query = query.Where((m, s) => authorizedIds.Contains(m.Id));
        }

        var data = await query
            .Select((m, s) => new MysamplesListOutput
            {
                id = m.Id,
                terminalid = m.Terminalid,
                organizationId = m.OrganizationId,
                positionId = m.PositionId,
                region = m.Region,
                agency = m.Agency,
                importantoxygen = SqlFunc.IIF(m.Importantoxygen == 0, "无", "有"),
                importantenjoy = SqlFunc.IIF(m.Importantenjoy == 0, "无", "有"),
                importantplentywater = SqlFunc.IIF(m.Importantplentywater == 0, "无", "有"),
                importantkitchen = SqlFunc.IIF(m.Importantkitchen == 0, "无", "有"),
                importantcommon = m.Importantcommon,
                importantcommonamount = m.Importantcommonamount,
                combinedSupply = SqlFunc.IIF(m.combinedSupply == 0, "无", "有"),
                bigwater = SqlFunc.IIF(m.bigwater == 0, "无", "有"),
            })
            .MergeTable()
            .OrderByIF(string.IsNullOrEmpty(input.sidx), it => it.id)
            .OrderByIF(!string.IsNullOrEmpty(input.sidx), it => input.sidx + " " + input.sort)
            .ToListAsync();
        var storeDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.Terminal);
        var levelDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Terminal, x => x.Level1);
        var signedclientDic = await _stores.AsQueryable().ToDictionaryAsync(x => x.Terminal, x => x.Signedclient);
        var organizeDic = await _organize.AsQueryable().ToDictionaryAsync(x => x.Id, x => x.FullName);

        foreach (var item in data)
        {
            if (string.IsNullOrEmpty(item.terminalid)) continue;

            if (storeDic.ContainsKey(item.terminalid))
            {
                item.terminalid = storeDic[item.terminalid].ToString();
            }

            if (levelDic.ContainsKey(item.terminalid))
            {
                if (levelDic[item.terminalid] is not null)
                    item.level1 = levelDic[item.terminalid].ToString();
                ;
            }

            if (signedclientDic.ContainsKey(item.terminalid))
            {
                if (signedclientDic[item.terminalid] is not null)
                    item.signedclient = signedclientDic[item.terminalid].ToString();
            }
        }

        if (!string.IsNullOrEmpty(input.terminalid))
        {
            data = data.Where(i => i.terminalid.Contains(input.terminalid)).ToList();
        }

        return data;
    }

    /// <summary>
    /// 导出门店信息.
    /// </summary>
    /// <param name="input">请求参数.</param>
    /// <returns></returns>
    [HttpGet("Actions/Export")]
    public async Task<dynamic> Export([FromQuery] MysamplesListQueryInput input)
    {
        var exportData = new List<MysamplesListOutput>();
        if (input.dataType == 0)
            exportData = Clay.Object(await GetList(input)).Solidify<PageResult<MysamplesListOutput>>().list;
        else
            exportData = await GetNoPagingList(input);
        List<ParamsModel> paramList =
            "[{\"value\":\"大区\",\"field\":\"region\"},{\"value\":\"办事处\",\"field\":\"agency\"},{\"value\":\"终端名称\",\"field\":\"terminalid\"},{\"value\":\"氧霂Pro\",\"field\":\"importantoxygen\"},{\"value\":\"安享\",\"field\":\"importantenjoy\"},{\"value\":\"壁挂炉出水体验\",\"field\":\"importantplentywater\"},{\"value\":\"全屋净水\",\"field\":\"importantkitchen\"},{\"value\":\"厨房情景区\",\"field\":\"importantcommon\"},{\"value\":\"普通烟灶数量\",\"field\":\"importantcommonamount\"},{\"value\":\"组织id\",\"field\":\"organizationId\"},{\"value\":\"大流量水伺服\",\"field\":\"bigwater\"},{\"value\":\"两/三联供\",\"field\":\"combinedSupply\"},{\"value\":\"平台商\",\"field\":\"signedclient\"},{\"value\":\"门店属性\",\"field\":\"level1\"}]"
                .ToList<ParamsModel>();
        ExcelConfig excelconfig = new ExcelConfig();
        excelconfig.FileName = "门店抽样管理.xls";
        excelconfig.HeadFont = "微软雅黑";
        excelconfig.HeadPoint = 10;
        excelconfig.IsAllSizeColumn = true;
        excelconfig.ColumnModel = new List<ExcelColumnModel>();
        foreach (var item in input.selectKey.Split(',').ToList())
        {
            var isExist = paramList.Find(p => p.field == item);
            if (isExist != null)
                excelconfig.ColumnModel.Add(
                    new ExcelColumnModel() { Column = isExist.field, ExcelColumn = isExist.value });
        }

        var addPath = FileVariable.TemporaryFilePath + excelconfig.FileName;
        ExcelExportHelper<MysamplesListOutput>.Export(exportData, excelconfig, addPath);
        var fileName = _userManager.UserId + "|" + addPath + "|xls";
        return new
        {
            name = excelconfig.FileName,
            url = "/api/File/Download?encryption=" + DESCEncryption.Encrypt(fileName, "JNPF")
        };
    }

    /// <summary>
    /// 批量删除门店信息.
    /// </summary>
    /// <param name="ids">主键数组.</param>
    /// <returns></returns>
    [HttpPost("batchRemove")]
    public async Task BatchRemove([FromBody] List<string> ids)
    {
        var entitys = await _repository.Context.Queryable<MysamplesEntity>().In(it => it.Id, ids).ToListAsync();
        if (entitys.Count > 0)
        {
            try
            {
                // 开启事务
                _db.BeginTran();

                // 批量删除门店信息
                await _repository.Context.Deleteable<MysamplesEntity>().In(it => it.Id, ids).ExecuteCommandAsync();

                // 清空门店出样明细-燃热表数据
                await _repository.Context.Deleteable<MysamplesburnEntity>()
                    .In(u => u.MainId, entitys.Select(s => s.Id).ToArray()).ExecuteCommandAsync();

                // 关闭事务
                _db.CommitTran();
            }
            catch (Exception)
            {
                // 回滚事务
                _db.RollbackTran();
                throw Oops.Oh(ErrorCode.COM1002);
            }
        }
    }

    /// <summary>
    /// 更新门店信息.
    /// </summary>
    /// <param name="id">主键值.</param>
    /// <param name="input">参数.</param>
    /// <returns></returns>
    [HttpPut("{id}")]
    public async Task Update(string id, [FromBody] MysamplesUpInput input)
    {
        var entity = input.Adapt<MysamplesEntity>();
        // 生成organizeId
        var organizeDic = await GetOrganizeDicTask();

        if (entity.Agency != null && organizeDic.ContainsKey(entity.Agency))
        {
            entity.OrganizationId = organizeDic[entity.Agency];
        }

        try
        {
            // 开启事务
            _db.BeginTran();

            await _repository.Context.Updateable<MysamplesEntity>(entity).IgnoreColumns(ignoreAllNullColumns: true)
                .ExecuteCommandAsync();

            // 清空门店出样明细-燃热原有数据
            await _repository.Context.Deleteable<MysamplesburnEntity>().Where(it => it.MainId == entity.Id)
                .ExecuteCommandAsync();

            // 新增门店出样明细-燃热新数据
            var mysamplesburnEntityList = input.mysamplesburnList.Adapt<List<MysamplesburnEntity>>();
            if (mysamplesburnEntityList != null)
            {
                foreach (var item in mysamplesburnEntityList)
                {
                    item.Id = SnowflakeIdHelper.NextId();
                    item.MainId = entity.Id;
                    item.Region = entity.Region;
                    item.Agency = entity.Agency;
                    item.Terminalid = entity.Terminalid;
                    item.OrganizationId = entity.OrganizationId;
                    item.LastModifyUserId = _userManager.UserId;
                    item.LastModifyTime = DateTime.Today;
                }

                await _repository.Context.Insertable<MysamplesburnEntity>(mysamplesburnEntityList)
                    .ExecuteCommandAsync();
            }

            // 关闭事务
            _db.CommitTran();
        }
        catch (Exception)
        {
            // 回滚事务
            _db.RollbackTran();
            throw Oops.Oh(ErrorCode.COM1001);
        }
    }

    /// <summary>
    /// 删除门店信息.
    /// </summary>
    /// <returns></returns>
    [HttpDelete("{id}")]
    public async Task Delete(string id)
    {
        if (!await _repository.Context.Queryable<MysamplesEntity>().AnyAsync(it => it.Id == id))
        {
            throw Oops.Oh(ErrorCode.COM1005);
        }

        try
        {
            // 开启事务
            _db.BeginTran();

            var entity = await _repository.AsQueryable().FirstAsync(it => it.Id.Equals(id));
            await _repository.Context.Deleteable<MysamplesEntity>().Where(it => it.Id.Equals(id)).ExecuteCommandAsync();

            // 清空门店出样明细-燃热表数据
            await _repository.Context.Deleteable<MysamplesburnEntity>().Where(it => it.MainId.Equals(entity.Id))
                .ExecuteCommandAsync();

            // 关闭事务
            _db.CommitTran();
        }
        catch (Exception)
        {
            // 回滚事务
            _db.RollbackTran();

            throw Oops.Oh(ErrorCode.COM1002);
        }
    }
}