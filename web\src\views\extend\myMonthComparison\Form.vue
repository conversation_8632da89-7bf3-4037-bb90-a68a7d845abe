<template>
	<transition name="el-zoom-in-center">
		<div class="JNPF-preview-main">
			<div class="JNPF-common-page-header">
				<el-page-header @back="goBack" :content="!dataForm.id ? '新建' : '编辑'" />
				<div class="options">
					<el-button type="primary" @click="dataFormSubmit()" :loading="btnLoading">确 定</el-button>
					<el-button @click="goBack">取 消</el-button>
				</div>
			</div>
			<div :style="{ margin: '0 auto', width: '100%' }" class="main">
				<!-- 基础信息卡片 -->
				<el-card class="form-card" shadow="hover">
					<div slot="header" class="card-header">
						<i class="el-icon-office-building"></i>
						<span>基础信息</span>
					</div>
					<el-form ref="elForm" :model="dataForm" size="medium" :label-width="labelWidth" label-position="right" :rules="rules">
						<el-row :gutter="24">
							<el-col :span="12" :xs="24" :sm="12" :md="12" :lg="12">
								<el-form-item label="大区" prop="region">
									<el-input
										v-model='dataForm.region'
										placeholder='请输入大区名称'
										clearable
										prefix-icon="el-icon-location" disabled>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="12" :lg="12">
								<el-form-item label="办事处" prop="agency">
									<el-input
										v-model='dataForm.agency'
										placeholder='请输入办事处名称'
										clearable
										prefix-icon="el-icon-office-building" disabled>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="12" :lg="12">
								<el-form-item label="终端门店" prop="terminal">
									<el-input
										v-model='dataForm.terminal'
										placeholder='请输入终端门店名称'
										clearable
										prefix-icon="el-icon-shop" disabled>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="12" :lg="12">
								<el-form-item label="门店属性" prop="level1">
									<el-input
										v-model='dataForm.level1'
										placeholder='门店属性'
										clearable
										prefix-icon="el-icon-shop" disabled>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="12" :lg="12">
								<el-form-item label="是否需要填写说明" prop="instructionNeeded">
									<el-select
										v-model='dataForm.instructionNeeded'
										placeholder='请选择'
										style="width: 100%"  disabled>
										<el-option label="是" value="是"></el-option>
										<el-option label="否" value="否"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
						</el-row>
					</el-form>
				</el-card>



				<!-- 预估数据卡片 -->
				<el-card class="form-card" shadow="hover">
					<div slot="header" class="card-header">
						<i class="el-icon-trend-charts"></i>
						<span>厨房预估数据</span>
					</div>
					<el-form ref="elForm3" :model="dataForm" size="medium" :label-width="labelWidth" label-position="right">
						<!-- 上上月厨房预估数据 -->
						<el-divider content-position="left">
							<span style="color: #fa8c16; font-weight: 600;">
								<i class="el-icon-date"></i> {{previousMonth}}月厨房预估数据
							</span>
						</el-divider>
						<el-row :gutter="24">
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item label="距离15%达标差距" prop="previousMonthTargetLinePercentagedistance">
									<el-input
										:value='formatTargetDistanceForDisplay(dataForm.previousMonthTargetLinePercentagedistance)'
										placeholder='请输入达标差距（如：-6.00或2.48）'
										clearable
										prefix-icon="el-icon-warning" disabled>
										<template slot="append">%</template>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item label="厨房预估达成月份" prop="previousMonthkitchenTargetMonth">
									<el-select
										v-model='dataForm.previousMonthkitchenTargetMonth'
										placeholder='请选择预估达成月份'
										style="width: 100%"
										clearable
										disabled>
										<el-option label="1月" value="1月"></el-option>
										<el-option label="2月" value="2月"></el-option>
										<el-option label="3月" value="3月"></el-option>
										<el-option label="4月" value="4月"></el-option>
										<el-option label="5月" value="5月"></el-option>
										<el-option label="6月" value="6月"></el-option>
										<el-option label="7月" value="7月"></el-option>
										<el-option label="8月" value="8月"></el-option>
										<el-option label="9月" value="9月"></el-option>
										<el-option label="10月" value="10月"></el-option>
										<el-option label="11月" value="11月"></el-option>
										<el-option label="12月" value="12月"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item label="厨房预估达成金额" prop="previousMonthkitchenTargetAmount">
									<el-input
										v-model='dataForm.previousMonthkitchenTargetAmount'
										placeholder='请输入预估达成金额'
										clearable
										prefix-icon="el-icon-money" disabled>
										<template slot="append">
											<el-tag type="danger" size="small">
												{{ dataForm.previousMonthkitchenTargetMonth ? `1月至${dataForm.previousMonthkitchenTargetMonth}` : '1月至预估月份' }}
											</el-tag>
										</template>
									</el-input>
								</el-form-item>
							</el-col>

						</el-row>
						<!-- 上月厨房预估数据 -->
						<el-divider content-position="left">
							<span style="color: #1890ff; font-weight: 600;">
								<i class="el-icon-date"></i> {{lastMonth}}月厨房预估数据
							</span>
						</el-divider>
						<el-row :gutter="24">
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item label="距离15%达标差距" prop="lastMonthTargetLinePercentagedistance">
									<el-input
										:value='formatTargetDistanceForDisplay(dataForm.lastMonthTargetLinePercentagedistance)'
										placeholder='请输入达标差距（如：-4.00或3.67）'
										clearable
										prefix-icon="el-icon-warning" disabled>
										<template slot="append">%</template>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item prop="lastMonthkitchenTargetMonth">
									<span slot="label" class="required-label">
										厨房预估达成月份
										<span class="required-note">须填写</span>
									</span>
									<el-select
										v-model='dataForm.lastMonthkitchenTargetMonth'
										placeholder='请选择预估达成月份'
										style="width: 100%"
										clearable>
										<el-option label="1月" value="1月"></el-option>
										<el-option label="2月" value="2月"></el-option>
										<el-option label="3月" value="3月"></el-option>
										<el-option label="4月" value="4月"></el-option>
										<el-option label="5月" value="5月"></el-option>
										<el-option label="6月" value="6月"></el-option>
										<el-option label="7月" value="7月"></el-option>
										<el-option label="8月" value="8月"></el-option>
										<el-option label="9月" value="9月"></el-option>
										<el-option label="10月" value="10月"></el-option>
										<el-option label="11月" value="11月"></el-option>
										<el-option label="12月" value="12月"></el-option>
									</el-select>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item prop="lastMonthkitchenTargetAmount">
									<span slot="label" class="required-label">
										厨房预估达成金额
										<span class="required-note">须填写</span>
									</span>
									<el-input
										v-model='dataForm.lastMonthkitchenTargetAmount'
										placeholder='请输入预估达成金额（仅限数字）'
										clearable
										prefix-icon="el-icon-money"
										@input="handleNumberInput('lastMonthkitchenTargetAmount', $event)"
										@keypress="handleKeyPress">
										<template slot="append">
											<el-tag type="danger" size="small">
												{{ dataForm.lastMonthkitchenTargetMonth ? `1月至${dataForm.lastMonthkitchenTargetMonth}` : '1月至预估月份' }}
											</el-tag>
										</template>
									</el-input>
								</el-form-item>
							</el-col>

						</el-row>

						<!-- 基础数据显示行 -->
						<el-divider content-position="left">
							<span style="color: #52c41a; font-weight: 600;">
								<i class="el-icon-data-line"></i> 基础数据
							</span>
						</el-divider>
						<el-row :gutter="15">
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item>
									<span slot="label" class="required-label">
										1至{{lastMonth}}月累计厨房销量
									</span>
									<el-input
										:value="dataForm.lastMonthTotalKitchenSales || '0.00'"
										readonly
										class="calculated-field-simple"
										prefix-icon="el-icon-data-board">
										<template slot="append">元</template>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item>
									<span slot="label" class="required-label">
										年度销售目标
									</span>
									<el-input
										:value="dataForm.targeSales || '0.00'"
										readonly
										class="calculated-field-simple"
										prefix-icon="el-icon-trophy">
										<template slot="append">元</template>
									</el-input>
								</el-form-item>
							</el-col>
						</el-row>

						<!-- 计算字段行 -->
						<el-divider content-position="left">
							<span style="color: #722ed1; font-weight: 600;">
								<i class="el-icon-s-operation"></i> 计算结果
							</span>
						</el-divider>
						<el-row :gutter="15">
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item>
									<span slot="label" class="required-label">
										厨房月均销售目标
									</span>
									<el-input
										:value="kitchenMonthlyTarget"
										readonly
										class="calculated-field-simple"
										prefix-icon="el-icon-s-data">
										<template slot="append">元</template>
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="12" :xs="24" :sm="12" :md="24" :lg="12">
								<el-form-item>
									<span slot="label" class="required-label">
										下个月厨房预估销售额
									</span>
									<el-input
										:value="nextMonthKitchenEstimate"
										readonly
										class="calculated-field-simple"
										prefix-icon="el-icon-trend-charts">
										<template slot="append">元</template>
									</el-input>
								</el-form-item>
							</el-col>

						</el-row>


					</el-form>
				</el-card>

				<!-- 改善措施卡片 -->
				<el-card class="form-card" shadow="hover">
					<div slot="header" class="card-header">
						<i class="el-icon-edit-outline"></i>
						<span>改善措施说明</span>
					</div>
					<el-form ref="elForm4" :model="dataForm" size="medium" :label-width="labelWidth" label-position="right">
						<el-row :gutter="24">
							<el-col :span="24">
								<el-form-item :label="`1至${previousMonth}月未达标及改善措施说明`" prop="previousMonthkitchenFailImproveNote">
									<el-input
										v-model='dataForm.previousMonthkitchenFailImproveNote'
										:placeholder='`请详细描述${previousMonth}月未达标情况及相应的改善措施...`'
										show-word-limit
										type='textarea'
										:autosize='{ "minRows": 4, "maxRows": 6 }'
										maxlength="500" disabled >
									</el-input>
								</el-form-item>
							</el-col>
							<el-col :span="24">
								<el-form-item prop="lastMonthkitchenFailImproveNote">
									<span slot="label" class="required-label">
										1至{{lastMonth}}月未达标及改善措施说明
										<span class="required-note">须填写</span>
									</span>
									<el-input
										v-model='dataForm.lastMonthkitchenFailImproveNote'
										:placeholder='`请详细描述${lastMonth}月未达标情况及相应的改善措施...`'
										show-word-limit
										type='textarea'
										:autosize='{ "minRows": 4, "maxRows": 6 }'
										maxlength="500">
									</el-input>
								</el-form-item>
							</el-col>

						</el-row>
					</el-form>
				</el-card>

				<!-- 销量趋势图表卡片 -->
				<el-card class="chart-card" shadow="hover">
					<div slot="header" class="card-header">
						<i class="el-icon-data-analysis"></i>
						<div class="chart-actions">
							<el-tooltip content="刷新图表" placement="top">
								<el-button
									type="text"
									icon="el-icon-refresh"
									@click="refreshChart"
									class="refresh-btn">
								</el-button>
							</el-tooltip>
						</div>
					</div>
					<div class="chart-container">
						<div id="kitchenChart" class="chart-content"></div>
					</div>
				</el-card>
			</div>
		</div>
	</transition>
</template>
<script>
import request from '@/utils/request'
import { getDictionaryDataSelector } from '@/api/systemData/dictionary'
import { getDataInterfaceRes } from '@/api/systemData/dataInterface'
import * as echarts from 'echarts'
export default {
	components: {},
	props: [],
	data() {
		return {
			btnLoading: false,
			loading: false,
			visible: false,
			currentMonth: new Date().getMonth() + 1, // 当前月份
			dataForm: {
				id: '',
				region: undefined,
				agency: undefined,
				terminal: undefined,
				level1: undefined,
				targeSales: undefined,
				lastMonthTotalSales: undefined,
				finishRate: undefined,
				instructionNeeded: undefined,
				lastMonthTotalBurnSales: undefined,
				lastMonthTotalBurnRate: undefined,
				lastMonthTotalHeatingSales: undefined,
				lastMonthTotalHeatingRate: undefined,
				lastMonthTotalKitchenSales: undefined,
				lastMonthTotalKitchenRate: undefined,
				lastMonthTotalElectricSales: undefined,
				lastMonthTotalElectricRate: undefined,
				lastMonthTotalWaterSales: undefined,
				lastMonthTotalWaterRate: undefined,
				lastMonthTotalAirSales: undefined,
				lastMonthTotalAirRate: undefined,
				lastMonthTargetLinePercentagedistance: undefined,
				lastMonthkitchenTargetMonth: undefined,
				lastMonthkitchenTargetAmount: undefined,
				previousMonthTotalBurnSales: undefined,
				previousMonthTotalBurnRate: undefined,
				previousMonthTotalHeatingSales: undefined,
				previousMonthTotalHeatingRate: undefined,
				previousMonthTotalKitchenSales: undefined,
				previousMonthTotalKitchenRate: undefined,
				previoustMonthTotalElectricSales: undefined,
				previoustMonthTotalElectricRate: undefined,
				previoustMonthTotalWaterSales: undefined,
				previoustMonthTotalWaterRate: undefined,
				previoustMonthTotalAirSales: undefined,
				previoustMonthTotalAirRate: undefined,
				previousMonthTargetLinePercentagedistance: undefined,
				previousMonthkitchenTargetMonth: undefined,
				previousMonthkitchenTargetAmount: undefined,
				lastMonthkitchenFailImproveNote: undefined,
				previousMonthkitchenFailImproveNote: undefined,
				creatorUserId: undefined,
				creatorTime: undefined,
				lastModifyUserId: undefined,
				lastModifyTime: undefined,
				organizationId: undefined,
			},
			rules: {
			},
		}
	},
	computed: {
		// 计算上月月份
		lastMonth() {
			let month = this.currentMonth - 1;
			if (month <= 0) {
				month = 12;
			}
			return month;
		},
		// 计算上上月月份
		previousMonth() {
			let month = this.currentMonth - 2;
			if (month <= 0) {
				month += 12;
			}
			return month;
		},
		// 响应式标签宽度
		labelWidth() {
			// 根据屏幕宽度动态调整标签宽度
			if (typeof window !== 'undefined') {
				const width = window.innerWidth;
				if (width <= 576) {
					return 'auto'; // 超小屏幕使用自动宽度
				} else if (width <= 768) {
					return '100px'; // 小屏幕使用更小宽度
				} else if (width <= 1024) {
					return '120px'; // 中小屏幕（如1024x600）
				} else if (width <= 1200) {
					return '140px'; // 中等屏幕
				} else {
					return '180px'; // 大屏幕使用较大宽度
				}
			}
			return '140px'; // 默认宽度
		},
		// 计算厨房月均销售目标
		kitchenMonthlyTarget() {
			const targetSales = this.dataForm.targeSales;
			if (!targetSales || targetSales <= 0) {
				return '0.00';
			}
			// 厨房月均销售目标 = 年销售目标 × 15% ÷ 12个月
			const monthlyTarget = (targetSales * 0.15) / 12;
			return monthlyTarget.toFixed(2);
		},
		// 计算下个月厨房预估销售额
		nextMonthKitchenEstimate() {
			const targetAmount = this.dataForm.lastMonthkitchenTargetAmount;
			const currentKitchenSales = this.dataForm.lastMonthTotalKitchenSales;
			const targetMonth = this.dataForm.lastMonthkitchenTargetMonth;

			console.log('=== Form 下个月厨房预估销售额计算 DEBUG ===');
			console.log('门店:', this.dataForm.terminal);
			console.log('厨房预估达成金额 (targetAmount):', targetAmount);
			console.log('1至上月累计厨房销量 (currentKitchenSales):', currentKitchenSales);
			console.log('厨房预估达成月份 (targetMonth):', targetMonth);

			// 检查必要数据是否存在 - 修改逻辑，允许currentKitchenSales为0
			if (!targetAmount || targetMonth === undefined || targetMonth === null || targetMonth === '') {
				console.log('返回0.00 - 缺少必要数据');
				return '0.00';
			}

			// 解析目标月份（去掉"月"字）
			const targetMonthNum = parseInt(targetMonth.replace('月', ''));
			if (isNaN(targetMonthNum)) {
				console.log('返回0.00 - 目标月份解析失败');
				return '0.00';
			}

			console.log('当前月份:', this.currentMonth);
			console.log('目标月份:', targetMonthNum);

			// 计算月度差
			let monthDiff = targetMonthNum - this.currentMonth;
			console.log('初始月度差:', monthDiff);

			// 处理跨年情况
			if (monthDiff <= 0) {
				monthDiff += 12;
				console.log('跨年处理后月度差:', monthDiff);
			}

			// 避免除零
			if (monthDiff <= 0) {
				console.log('返回0.00 - 月度差小于等于0');
				return '0.00';
			}

			// 计算下个月厨房预估销售额 - 允许currentKitchenSales为0或undefined
			const currentSales = currentKitchenSales || 0;
			const remainingAmount = targetAmount - currentSales;
			const nextMonthEstimate = remainingAmount / monthDiff;

			console.log('当前累计销量 (处理后):', currentSales);
			console.log('剩余销售额:', remainingAmount);
			console.log('剩余月份数:', monthDiff);
			console.log('下个月预估销售额:', nextMonthEstimate);

			// 处理负数情况
			if (nextMonthEstimate < 0) {
				console.log('返回0.00 - 计算结果为负数');
				return '0.00';
			}

			const result = nextMonthEstimate.toFixed(2);
			console.log('最终结果:', result);
			console.log('=== Form 计算结束 ===');
			return result;
		}
	},
	watch: {
		// 移除了导致重复乘以100的监听器逻辑
		// 现在只保留基本的数据监听，不做自动转换
	},
	created() {
	},
	mounted() {
		// 监听窗口大小变化，更新标签宽度
		this.handleResize = () => {
			this.$forceUpdate(); // 强制更新以重新计算labelWidth
		};
		window.addEventListener('resize', this.handleResize);
	},
	beforeDestroy() {
		// 清理事件监听器
		if (this.handleResize) {
			window.removeEventListener('resize', this.handleResize);
		}
	},
	methods: {
		// 格式化距离15%达标差距字段用于显示（仅显示，不影响数据存储）
		formatTargetDistanceForDisplay(value) {
			if (value === null || value === undefined || value === '' || value === 'NaN' || value === 'Infinity') {
				return '';
			}
			const numValue = parseFloat(value);
			if (isNaN(numValue)) {
				return '';
			}
			// 将小数转换为百分比显示（如 0.1395 -> 13.95）
			return (numValue * 100).toFixed(2);
		},

		// 处理数字输入限制
		handleNumberInput(fieldName, value) {
			// 移除非数字字符（保留小数点）
			const numericValue = value.replace(/[^\d.]/g, '');

			// 确保只有一个小数点
			const parts = numericValue.split('.');
			let cleanValue = parts[0];
			if (parts.length > 1) {
				cleanValue += '.' + parts.slice(1).join('');
			}

			// 更新数据
			this.$set(this.dataForm, fieldName, cleanValue);
		},

		// 处理按键事件，只允许数字、小数点、退格、删除等
		handleKeyPress(event) {
			const char = String.fromCharCode(event.which);
			const allowedKeys = ['Backspace', 'Delete', 'Tab', 'Escape', 'Enter', 'Home', 'End', 'ArrowLeft', 'ArrowRight', 'Clear', 'Copy', 'Paste'];

			// 允许控制键
			if (allowedKeys.includes(event.key) || event.ctrlKey || event.metaKey) {
				return;
			}

			// 只允许数字和小数点
			if (!/[\d.]/.test(char)) {
				event.preventDefault();
			}

			// 防止多个小数点
			if (char === '.' && event.target.value.includes('.')) {
				event.preventDefault();
			}
		},

		// 格式化百分比字段
		formatPercentageField(value) {
			console.log('=== 距离15%达标差距 DEBUG: formatPercentageField 调用 ===');
			console.log('输入值:', value);
			console.log('输入值类型:', typeof value);

			if (value === null || value === undefined || value === '') {
				console.log('返回空字符串（值为空）');
				return '';
			}
			const strVal = value.toString();
			console.log('字符串值:', strVal);

			if (strVal.includes('Infinity') || strVal === 'NaN') {
				console.log('返回空字符串（包含Infinity或NaN）');
				return '';
			}
			const num = parseFloat(value);
			console.log('解析的数字:', num);

			if (!isNaN(num)) {
				const result = num.toFixed(2);
				console.log('格式化结果:', result);
				return result;
			}
			console.log('返回原值（无法解析为数字）');
			return value;
		},

		// 格式化所有百分比字段 - 简化版本，避免重复转换
		formatPercentageFields() {
			console.log('=== 距离15%达标差距 DEBUG: formatPercentageFields 开始 ===');
			console.log('格式化前 lastMonthTargetLinePercentagedistance:', this.dataForm.lastMonthTargetLinePercentagedistance);
			console.log('格式化前 previousMonthTargetLinePercentagedistance:', this.dataForm.previousMonthTargetLinePercentagedistance);

			// 只做基本的无效值清理，不做数值转换
			if (this.dataForm.lastMonthTargetLinePercentagedistance === 'Infinity' ||
				this.dataForm.lastMonthTargetLinePercentagedistance === 'NaN') {
				this.dataForm.lastMonthTargetLinePercentagedistance = '';
			}

			if (this.dataForm.previousMonthTargetLinePercentagedistance === 'Infinity' ||
				this.dataForm.previousMonthTargetLinePercentagedistance === 'NaN') {
				this.dataForm.previousMonthTargetLinePercentagedistance = '';
			}

			console.log('格式化后 lastMonthTargetLinePercentagedistance:', this.dataForm.lastMonthTargetLinePercentagedistance);
			console.log('格式化后 previousMonthTargetLinePercentagedistance:', this.dataForm.previousMonthTargetLinePercentagedistance);
			console.log('=== formatPercentageFields 结束 ===');
		},

		goBack() {
			this.$emit('refresh')
		},
		init(id) {
			console.log('=== 距离15%达标差距 DEBUG: Form init 开始 ===');
			console.log('ID:', id);

			this.dataForm.id = id || 0;
			this.visible = true;
			this.$nextTick(() => {
				// 重置所有表单
				const formRefs = ['elForm', 'elForm3', 'elForm4'];
				formRefs.forEach(ref => {
					if (this.$refs[ref]) {
						this.$refs[ref].resetFields();
					}
				});

				if (this.dataForm.id) {
					console.log('=== 距离15%达标差距 DEBUG: 加载现有数据 ===');
					request({
						url: '/api/SubDev/MyMonthComparison/' + this.dataForm.id,
						method: 'get'
					}).then(res => {
						console.log('=== 距离15%达标差距 DEBUG: Form API 响应 ===');
						console.log('完整响应数据:', res.data);
						console.log('lastMonthTargetLinePercentagedistance 原始值:', res.data.lastMonthTargetLinePercentagedistance);
						console.log('previousMonthTargetLinePercentagedistance 原始值:', res.data.previousMonthTargetLinePercentagedistance);

						this.dataForm = res.data;

						console.log('=== 距离15%达标差距 DEBUG: 数据设置后 ===');
						console.log('dataForm.lastMonthTargetLinePercentagedistance:', this.dataForm.lastMonthTargetLinePercentagedistance);
						console.log('dataForm.previousMonthTargetLinePercentagedistance:', this.dataForm.previousMonthTargetLinePercentagedistance);

						// 格式化百分比字段，避免显示Infinity
						this.formatPercentageFields();

						console.log('=== 距离15%达标差距 DEBUG: 格式化后 ===');
						console.log('dataForm.lastMonthTargetLinePercentagedistance:', this.dataForm.lastMonthTargetLinePercentagedistance);
						console.log('dataForm.previousMonthTargetLinePercentagedistance:', this.dataForm.previousMonthTargetLinePercentagedistance);

						// 在加载数据后初始化图表
						this.$nextTick(() => {
							this.initChart();
						});
					}).catch(error => {
						console.log('=== 距离15%达标差距 DEBUG: 加载数据失败 ===', error);
						// 加载失败也要初始化空图表
						this.initChart();
					})
				} else {
					console.log('=== 距离15%达标差距 DEBUG: 新建模式 ===');
					// 如果是新建，也初始化一个空图表
					this.initChart();
				}
			});
			this.$store.commit('generator/UPDATE_RELATION_DATA', {})
		},
		// 初始化折线图
		initChart() {
			// 延迟一下确保数据已加载
			this.$nextTick(() => {
				// 如果有门店信息，从后端获取真实数据
				if (this.dataForm.terminal && this.dataForm.terminal.trim() !== '') {
					this.loadChartDataFromAPI();
				} else {
					// 否则显示空图表
					this.renderChart({});
				}
			});
		},

		// 从后端API加载图表数据
		async loadChartDataFromAPI() {
			try {
				// 再次检查门店名称
				if (!this.dataForm.terminal || this.dataForm.terminal.trim() === '') {
					this.renderChart({});
					return;
				}

				const requestData = {
					terminal: this.dataForm.terminal.trim(),
					months: 12
				};

				const response = await request({
					url: '/api/SubDev/MyMonthComparison/GetKitchenSalesChartData',
					method: 'GET',
					data: requestData
				});

				this.renderChart(response.data);
			} catch (error) {
				// 如果API调用失败，显示模拟数据
				const mockData = this.getMockKitchenData();
				this.renderChart({
					months: mockData.months,
					kitchenButWaterSales: mockData.values,
					terminal: this.dataForm.terminal || '未知门店'
				});
			}
		},

		// 渲染图表
		renderChart(chartData) {
			this.$nextTick(() => {
				const chartDom = document.getElementById('kitchenChart');
				if (!chartDom) return;

				const myChart = echarts.init(chartDom);

				// 处理图表数据
				const months = chartData.months || [];
				const kitchenButWaterSales = chartData.kitchenButWaterSales || [];
				const hasData = months.length > 0 && kitchenButWaterSales.length > 0;

				const option = {
					title: {
						text: hasData ? `${chartData.terminal || '门店'} - 厨房(不含净水)销量趋势` : '暂无数据',
						left: 'center',
						textStyle: {
							color: '#303133',
							fontSize: 16,
							fontWeight: 600
						}
					},
					tooltip: {
						trigger: 'axis',
						formatter: function(params) {
							if (params && params.length > 0) {
								const data = params[0];
								return `${data.name}<br/>${data.seriesName}: ${data.value.toLocaleString()}元`;
							}
							return '';
						},
						backgroundColor: '#ffffff',
						borderColor: '#1890ff',
						borderWidth: 1,
						textStyle: {
							color: '#303133'
						}
					},
					xAxis: {
						type: 'category',
						data: months,
						axisLabel: {
							rotate: 45,
							color: '#606266',
							fontSize: 12
						},
						axisLine: {
							lineStyle: {
								color: '#e4e7ed'
							}
						}
					},
					yAxis: {
						type: 'value',
						name: '销售额(元)',
						nameTextStyle: {
							color: '#606266'
						},
						axisLabel: {
							color: '#606266',
							formatter: function(value) {
								if (value >= 10000) {
									return (value / 10000).toFixed(1) + '万';
								}
								return value.toLocaleString();
							}
						},
						axisLine: {
							lineStyle: {
								color: '#e4e7ed'
							}
						},
						splitLine: {
							lineStyle: {
								color: '#f0f2f5'
							}
						}
					},
					series: hasData ? [
						{
							name: '厨房(不含净水)销量',
							type: 'line',
							data: kitchenButWaterSales,
							lineStyle: {
								color: '#1890ff',
								width: 3
							},
							itemStyle: {
								color: '#1890ff'
							},
							symbolSize: 8,
							symbol: 'circle',
							markPoint: {
								data: [
									{ type: 'max', name: '最大值' },
									{ type: 'min', name: '最小值' }
								],
								itemStyle: {
									color: '#1890ff'
								}
							},
							markLine: {
								data: [
									{ type: 'average', name: '平均值' }
								],
								lineStyle: {
									color: '#52c41a'
								}
							}
						}
					] : [],
					grid: {
						left: '3%',
						right: '4%',
						bottom: '15%',
						top: '15%',
						containLabel: true
					},
					graphic: !hasData ? {
						type: 'text',
						left: 'center',
						top: 'middle',
						style: {
							text: '暂无销量数据',
							fontSize: 16,
							fill: '#999'
						}
					} : null
				};

				myChart.setOption(option);

				// 响应式调整
				window.addEventListener('resize', function () {
					myChart.resize();
				});
			});
		},
		// 获取模拟的厨房销量数据
		getMockKitchenData() {
			// 获取当前日期
			const now = new Date();
			const currentYear = now.getFullYear();
			const currentMonth = now.getMonth() + 1;

			// 生成最近12个月的月份标签
			const months = [];
			const values = [];

			for (let i = 11; i >= 0; i--) {
				let month = currentMonth - i;
				let year = currentYear;

				if (month <= 0) {
					month += 12;
					year -= 1;
				}

				months.push(`${year}年${month}月`);

				// 生成随机销量数据，有上升趋势
				let baseValue = 50000 + Math.random() * 20000; // 基础值
				let trendValue = (11 - i) * 5000; // 趋势值，月份越近值越大
				let randomFactor = Math.random() * 10000 - 5000; // 随机因子

				let value = Math.round(baseValue + trendValue + randomFactor);

				// 如果有真实数据，可以在这里使用
				if (this.dataForm.id && month === currentMonth - 1) {
					// 上月数据
					value = this.dataForm.lastMonthTotalKitchenButWaterSales || value;
				} else if (this.dataForm.id && month === currentMonth - 2) {
					// 上上月数据
					value = this.dataForm.previousMonthTotalKitchenButWaterSales || value;
				}

				values.push(value);
			}

			return { months, values };
		},
		dataFormSubmit() {
			// 验证所有表单
			const formRefs = ['elForm', 'elForm3', 'elForm4'];
			let allValid = true;

			const validatePromises = formRefs.map(ref => {
				return new Promise((resolve) => {
					if (this.$refs[ref]) {
						this.$refs[ref].validate((valid) => {
							if (!valid) allValid = false;
							resolve(valid);
						});
					} else {
						resolve(true);
					}
				});
			});

			Promise.all(validatePromises).then(() => {
				if (allValid) {
					console.log('=== 距离15%达标差距 DEBUG: 表单提交开始 ===');
					console.log('提交的数据:', this.dataForm);
					console.log('lastMonthTargetLinePercentagedistance:', this.dataForm.lastMonthTargetLinePercentagedistance);
					console.log('previousMonthTargetLinePercentagedistance:', this.dataForm.previousMonthTargetLinePercentagedistance);

					this.btnLoading = true;
					if (!this.dataForm.id) {
						console.log('=== 新增模式提交 ===');
						request({
							url: `/api/SubDev/MyMonthComparison`,
							method: 'post',
							data: this.dataForm,
						}).then((res) => {
							console.log('=== 新增提交响应 ===', res);
							this.$message({
								message: res.msg,
								type: 'success',
								duration: 1000,
								onClose: () => {
									this.btnLoading = false;
									this.visible = false,
										this.$emit('refresh', true)
								}
							})
						}).catch(() => {
							this.btnLoading = false;
						})
					} else {
						console.log('=== 编辑模式提交 ===');
						request({
							url: '/api/SubDev/MyMonthComparison/' + this.dataForm.id,
							method: 'PUT',
							data: this.dataForm
						}).then((res) => {
							console.log('=== 编辑提交响应 ===', res);
							this.$message({
								message: res.msg,
								type: 'success',
								duration: 1000,
								onClose: () => {
									this.btnLoading = false;
									this.visible = false
									this.$emit('refresh', true)
								}
							})
						}).catch(() => {
							this.btnLoading = false;
						})
					}
				} else {
					this.$message.warning('请检查表单填写是否正确');
				}
			});
		},

		// 手动刷新图表
		refreshChart() {
			console.log('=== 手动刷新图表 ===');
			console.log('当前门店:', this.dataForm.terminal);

			if (this.dataForm.terminal && this.dataForm.terminal.trim() !== '') {
				this.loadChartDataFromAPI();
			} else {
				this.$message.warning('请先选择门店');
			}
		}
	}
}
</script>

<style lang="scss" scoped>
// 使用项目标准的蓝白色配色
.form-card, .chart-card {
	margin-bottom: 20px;
	border-radius: 8px;
	border: 1px solid #dcdfe6;
	transition: all 0.3s ease;

	&:hover {
		box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
		border-color: #1890ff;
	}

	.card-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		font-size: 16px;
		font-weight: 600;
		color: #303133;
		padding: 0;

		i {
			margin-right: 8px;
			font-size: 18px;
			color: #1890ff;
		}

		.chart-actions {
			.refresh-btn {
				color: #1890ff;
				font-size: 14px;
				padding: 6px;
				border-radius: 4px;
				transition: all 0.3s ease;

				&:hover {
					background: #ecf5ff;
					color: #409eff;
				}
			}
		}
	}
}

.chart-card {
	.chart-container {
		padding: 20px 0;

		.chart-content {
			width: 100%;
			height: 400px;
			border-radius: 6px;
			background: #ffffff;
			border: 1px solid #e4e7ed;
		}
	}
}

// 表单样式优化 - 使用标准蓝白色配色
::v-deep .el-form {
	.el-form-item {
		margin-bottom: 20px;

		.el-form-item__label {
			color: #303133;
			font-weight: 500;
		}

		.el-input, .el-select, .el-date-editor {
			.el-input__inner, .el-input__wrapper {
				border-radius: 4px;
				border: 1px solid #dcdfe6;
				transition: all 0.3s ease;

				&:focus {
					border-color: #1890ff;
					box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
				}
			}

			.el-input__prefix, .el-input__suffix {
				color: #1890ff;
			}
		}

		.el-textarea {
			.el-textarea__inner {
				border-radius: 4px;
				border: 1px solid #dcdfe6;
				transition: all 0.3s ease;

				&:focus {
					border-color: #1890ff;
					box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
				}
			}
		}
	}
}

// 卡片样式
::v-deep .el-card__body {
	padding: 20px;
}

::v-deep .el-card__header {
	background: #fafafa;
	border-bottom: 1px solid #e4e7ed;
	padding: 16px 20px;
}

// 必填字段样式
.required-label {
	color: #f5222d;
	font-weight: 600;

	.required-note {
		font-size: 12px;
		color: #f5222d;
		margin-left: 4px;
		font-weight: normal;
		background: #fff2f0;
		padding: 2px 6px;
		border-radius: 3px;
		border: 1px solid #ffccc7;
	}
}

/* 响应式布局优化 */
@media (max-width: 1024px) {
	::v-deep .el-form {
		.el-form-item__label {
			text-align: left !important;
			padding-right: 6px !important;
			font-size: 13px !important;
			min-width: 100px !important;
		}

		.el-form-item {
			margin-bottom: 16px;
		}

		.el-input, .el-select {
			.el-input__inner {
				padding: 0 12px !important;
				font-size: 13px !important;
			}
		}
	}

	.required-label {
		font-size: 13px !important;

		.required-note {
			font-size: 11px !important;
			padding: 1px 3px !important;
		}
	}
}

@media (max-width: 768px) {
	::v-deep .el-form {
		.el-form-item {
			margin-bottom: 18px;

			.el-form-item__label {
				line-height: 1.4 !important;
				padding-bottom: 8px !important;
				min-width: auto !important;
				width: 100% !important;
				text-align: left !important;
				padding-right: 0 !important;
			}
		}

		.el-input, .el-select {
			width: 100% !important;

			.el-input__inner, .el-select__inner {
				padding: 0 15px !important;
			}
		}
	}

	.required-label {
		font-size: 14px;
		line-height: 1.4;

		.required-note {
			display: block;
			margin-left: 0;
			margin-top: 4px;
		}
	}

	::v-deep .el-divider {
		margin: 16px 0 !important;

		.el-divider__text {
			font-size: 14px !important;
		}
	}

	.card-header {
		padding: 12px 16px !important;

		span {
			font-size: 15px !important;
		}
	}
}

@media (max-width: 576px) {
	::v-deep .el-form {
		.el-form-item {
			margin-bottom: 20px;

			.el-form-item__label {
				font-size: 13px !important;
				line-height: 1.3 !important;
				margin-bottom: 6px !important;
			}
		}

		.el-input, .el-select, .el-textarea {
			.el-input__inner, .el-textarea__inner {
				font-size: 14px !important;
				padding: 8px 12px !important;
			}
		}
	}

	.required-label {
		font-size: 13px;

		.required-note {
			font-size: 11px;
			padding: 1px 4px;
		}
	}

	::v-deep .el-col {
		margin-bottom: 0;
		padding: 0 8px !important;
	}

	::v-deep .el-card {
		margin-bottom: 16px;

		.el-card__body {
			padding: 16px !important;
		}
	}

	::v-deep .el-divider {
		margin: 12px 0 !important;

		.el-divider__text {
			font-size: 13px !important;
			padding: 0 8px !important;
		}
	}

	.card-header {
		padding: 10px 12px !important;

		i {
			font-size: 14px !important;
		}

		span {
			font-size: 14px !important;
		}
	}

	.chart-container {
		padding: 8px !important;

		.chart-content {
			height: 250px !important;
		}
	}

	// 简洁的计算字段输入框样式
	.calculated-field-simple {
		::v-deep .el-input__inner {
			background-color: #f5f5f5 !important;
			border-color: #d9d9d9 !important;
			color: #333 !important;
			font-weight: 600 !important;
		}

		::v-deep .el-input-group__append {
			background-color: #f0f0f0 !important;
			color: #666 !important;
			border-color: #d9d9d9 !important;
			font-weight: 600 !important;
		}

		::v-deep .el-input__prefix {
			color: #999 !important;
		}
	}
}
</style>
